pub mod cache;
pub mod excluder;
pub mod mev;
pub mod display_util;
pub mod index;
pub mod value_calc;
pub mod sync;
pub mod pools;

use alloy::{consensus::Transaction as ConsensusTransaction, eips::BlockId, primitives::{Address, U256}, providers::Provider, rpc::types::{Filter, Transaction}};
use colored::Colorize;
use index::Index;
use serde::{Deserialize, Serialize};
use tokio::{sync::mpsc, time::sleep};
use tokio_stream::wrappers::ReceiverStream;
use std::{collections::{ HashMap, HashSet}, fs::File, io::{BufReader, BufWriter, Read, Write}, pin::Pin, sync::{atomic::{AtomicBool, AtomicU64}, Arc}, time::Duration, vec};
use flate2::read::GzDecoder;
use flate2::write::GzEncoder;
use flate2::Compression;
use crate::{connector::Connector, tools::now_str, vira::{consts::{GET_LOGS_TIMEOUT, MAX_MEV_COUNT}, dex::factory::DexFactory, pool::{DexPool, Status, SwapWay, POOL}, status::pools::Pools, token::{Token, TokenManager}}, CONFIG};
use cache::Cache;
use mev::{Mev, MevPool, MevStatus};
use futures::{future::ok, stream::{iter, StreamExt}, Stream};
use std::sync::atomic::{AtomicUsize, Ordering};
use super::{consts::BATCH_CHECK_SIZE, dex::factory::FACTORY, errors::{CheckpointError, DEXError}};


//get_mev的临时数据结构
#[derive(Debug, Clone, Default, Serialize, Deserialize)]
pub struct PoolIndex {
    pub addr : Address,
    pub in_index : usize,
    pub out_index : usize,

    pub in_token : Address,
    pub out_token : Address,
}



impl PoolIndex {
    //反转参数
    pub fn reverse(&self) -> PoolIndex {
        PoolIndex {
            addr: self.addr,
            in_index: self.out_index,
            out_index: self.in_index,
            in_token: self.out_token,
            out_token: self.in_token,
        }
    }

    pub fn to_no_fee_mev_pool(&self) -> MevPool {
        MevPool {
            addr: self.addr,
            in_index: self.in_index,
            out_index: self.out_index,
            fee : U256::ZERO,
            fee_desc : U256::ZERO,
        }
    }
}


#[derive(Default, Serialize, Deserialize)]
pub struct StatusManager {
    //load from cache
    pub factory_synced_block : u64,
    pub factories: HashMap<Address, FACTORY>,

    pub pools : Arc<Pools>,
    
    #[serde(skip_serializing, skip_deserializing)]
    pub tokens : TokenManager,
    
    #[serde(skip_serializing, skip_deserializing)]
    pub index : Index,
    
    #[serde(skip_serializing, skip_deserializing)]
    pub inited : AtomicBool, //是否更新完pool数据

}

impl StatusManager {
    pub fn new() -> StatusManager {
        let path = CONFIG.files.checkpoint.clone();
        let mut sm = StatusManager {
            tokens: TokenManager::new(),
            inited: AtomicBool::new(false),
            ..Default::default()
        };

        // Try to load from cache
        match File::open(&path) {
            Ok(file) => {
                let mut reader = BufReader::new(file);
                
                // 尝试读取文件内容并解压缩
                let content = match Self::read_and_decompress(&mut reader) {
                    Ok(content) => content,
                    Err(e) => {
                        println!("[ERR] Failed to read or decompress cache: {}", e);
                        // Initialize from config if decompression fails
                        sm.initialize_from_config();
                        return sm;
                    }
                };
                
                match serde_json::from_str::<StatusManager>(&content) {
                    Ok(cache) => {
                        // Cache exists, load data from cache
                        println!("loading status manager from cache: {}", CONFIG.files.checkpoint.clone());
                        sm = cache;
                        // 收集池子以进行迭代，避免借用检查器问题
                        let pools_to_add: Vec<_> = sm.pools.data.iter().map(|p| p.value().clone()).collect();
                        // 为每个池子重新运行add函数以生成tokens和index
                        for pool in pools_to_add {
                            sm.add(pool);
                        }
                    }
                    Err(e) => {
                        println!("[ERR] Failed to deserialize cache: {}", e);
                        // Initialize from config if deserialization fails
                        sm.initialize_from_config();
                    }
                }
            }
            Err(_) => {
                // Cache doesn't exist, initialize from config
                sm.initialize_from_config();
            }
        }
        sm
    }

    /// 读取并解压缩文件内容
    /// 支持gzip压缩格式，也兼容未压缩的旧缓存文件
    fn read_and_decompress(reader: &mut BufReader<File>) -> Result<String, Box<dyn std::error::Error>> {
        // 读取文件内容
        let mut compressed_data = Vec::new();
        reader.read_to_end(&mut compressed_data)?;
        
        // 检查是否是gzip格式（通过魔数判断）
        if compressed_data.len() >= 2 && compressed_data[0] == 0x1f && compressed_data[1] == 0x8b {
            // 是gzip格式，进行解压缩
            let mut decoder = GzDecoder::new(&compressed_data[..]);
            let mut decompressed_data = String::new();
            decoder.read_to_string(&mut decompressed_data)?;
            Ok(decompressed_data)
        } else {
            // 不是gzip格式，假设是未压缩的文本
            let content = String::from_utf8(compressed_data)?;
            Ok(content)
        }
    }

    fn initialize_from_config(&mut self) {
        // Initialize factories from config
        for factory in &CONFIG.factories {
            self.factories.insert(factory.data().addr, factory.clone());
        }
    }

    /// 初始化
    /// 逻辑:
    /// 1. 检查 CONFIG 中是否存在当前缓存中没有的 factory 地址，如果有则添加并同步
    /// 2. 如果pools为空, 则同步新的pool
    /// 3. 生成所有pool的mev
    /// 4. 同步所有pool的reserves
    pub async fn init(&mut self, connector: Arc<Connector>) -> Result<(), DEXError> {
        // 检查并添加config中新的 factory
        let mut has_new_factories = false;
        for config_factory in &CONFIG.factories {
            let factory_addr = config_factory.data().addr;
            if !self.factories.contains_key(&factory_addr) {
                println!("发现新的 factory: {} ({})",
                    factory_addr,
                    config_factory.data().name.yellow()
                );
                self.factories.insert(factory_addr, config_factory.clone());
                has_new_factories = true;
            }
        }

        if self.pools.data.len() == 0 || has_new_factories {
            println!("syncing new pools");
            let new_addrs = sync::sync_new_pools(self, connector.clone()).await?;
            println!("discovered {} new pools.", new_addrs.len());
            let _ = self.save();
            tokio::time::sleep(tokio::time::Duration::from_secs(3)).await;

            //过滤new_addrs, 返回只存在于pools.data中的地址
            let new_addrs = new_addrs.into_iter().filter(|a| self.pools.data.contains_key(a)).collect::<Vec<_>>();
            //None:全量更新 Some:增量更新
            let mode = if self.pools.data.len() == 0 { None } else { Some(new_addrs) };
            self.generate_mevs(mode).await;
            let _ = self.save();
        }
        Ok(())
    }

    pub fn save(&self) -> Result<(), CheckpointError> {
        let path = CONFIG.files.checkpoint.clone();
        println!("{}", format!("saving checkpoint to path: {}， pools len: {}", path, self.pools.data.len()).green());
        
        // 序列化数据
        let json_data = serde_json::to_string(self)?;
        
        // 创建文件
        let file = File::create(path)?;
        
        // 使用GzEncoder进行gzip压缩
        let mut encoder = GzEncoder::new(file, Compression::default());
        encoder.write_all(json_data.as_bytes())?;
        encoder.finish()?;
        
        Ok(())
    }

    //加入pool到pools，生成tokens和index数据
    pub fn add(&mut self, pool: POOL) -> bool {
        let status = pool.data().status.clone();
        if status != Status::OddGood && status != Status::Good && status != Status::Fee  { return false; }
        //TODO: 测试暂时隐藏
        //去除活跃时间在2个月前的pair, 当前时间戳减去update_time
        //if tools::now_ts_sec() - pool.data().update_time > 2 * 30 * 24 * 60 * 60 { return false; }

        self.index.add(&pool);
        let data = pool.data();
        let addr = data.addr;
        data.tokens.iter().for_each(|t| {
            if !self.tokens.data.contains_key(&t.addr) {
                let token_config = CONFIG.tokens.get(&t.addr);

                let (price, is_eth) = if let Some(config) = token_config {
                    (config.price, config.is_eth)
                } else {
                    (1.0, false)
                };

                self.tokens.add(Token {
                    addr: t.addr,
                    symbol: t.symbol.to_string(),
                    decimals: t.decimal,
                    price,
                    is_eth
                });
            }
        });

        //self.data.entry(addr).or_insert_with(||{ pool });
        // 使用 insert 的返回值判断是否为新增
        self.pools.data.insert(addr, pool).is_none()
        //warn!("pair already exists: {:?}", addr);
        //Ok(())
    }

    pub fn get_mev(&self, addr : &Address) -> Vec<Mev>{
        //不参与后续的mev path
        let d_data = self.pools.data.get(addr).expect(format!("pool not found: {:?}", addr).as_str());
        let main_pool = d_data.data();
        let is_eoa = main_pool.swap_way == SwapWay::RouterByEoa;

        let mut paths: Vec<Mev> = Vec::with_capacity(MAX_MEV_COUNT);
        let stables = CONFIG.stable_matrix.clone();
        //let mut consume = Consume::new();

        let token0 = main_pool.tokens[0].addr;
        let token1 = main_pool.tokens[1].addr;
        let main_pool_index = PoolIndex {
            addr: *addr,
            in_index: 0,
            out_index: 1,
            in_token: token0.clone(),
            out_token: token1.clone(),
        };
        //let mut filter = Filter::new(addr);

        let mut cache = Cache::new(addr);
        
        'mev : for _ in 0..MAX_MEV_COUNT {
            //let mut exclude = Excluder::new();
            //exclude.insert_all(*addr);
            //filter.add_exclude(addr);

            //最大价值的mevpath
            let mut max_value_mev = Mev::default();
            let mut max_values = vec![];
            let mut max_lowest = U256::ZERO;

            for stable in &stables {
                //let stable = s.iter().map(|s| s.as_str()).collect::<Vec<&str>>();
                let mut path = vec![main_pool_index.clone()];

                //扩展右边, 右边速度比左边快
                let s_out = if stable.contains(&token1) {
                    token1
                } else {
                    let right = self.index.get_path(&token1, stable, &self.pools, Some(&cache), None, is_eoa);
                    //如果找不到则跳过当前的stable
                    if right.is_empty() { continue; }

                    //exclude.batch_insert_all(&right);
                    //filter.batch_add_exclude(&right);
                    path.extend(right.iter().map(|&p| p.clone()));
                    //Address::ZERO
                    right.last().unwrap().out_token
                };
                //dbg!(&path);

                //扩展左边
                let s_in = if stable.contains(&token0) {
                    token0
                } else {
                    let left = self.index.get_path(&token0, stable, &self.pools, Some(&cache), Some(&path), is_eoa);
                    //如果找不到则跳过当前的stable
                    if left.is_empty() { continue; }
                    let left: Vec<_> = left.into_iter().rev().map(|x| x.reverse()).collect();
                    path.splice(0..0, left.iter().cloned());
                    //Address::ZERO
                    left[0].in_token
                };

                //检查path中是否有重复的地址
                let mut seen_addresses = HashSet::new();
                if path.iter().any(|p| !seen_addresses.insert(p.addr)) {
                    panic!("[ERR] Duplicate address found in path: {:?}", path);
                }
                drop(seen_addresses);


                //dbg!(&path);
                //计算方式, 见ViraData.sol
                let calc = if path.iter().any(|p| {
                    let ver = self.pools.data.get(&p.addr).unwrap().data().ver;
                    ver == 3 || ver == 4
                }){
                    3 //no preview, 固定amountIn
                } else if path.iter().all(|p| {
                    let ver = self.pools.data.get(&p.addr).unwrap().data().ver;
                    ver == 1 || ver == 2
                }) {
                    0 //v1和v2, 使用dydx求最大值
                } else {
                    2 //固定amountIn, 但是有preview
                };

                //println!("-------------------------------");
                let (values, lowest) = value_calc::ValueCalc::calc_lowest_value(&path, &self.pools, Some(&cache));
                
                // 添加调试日志，记录lowest值信息
                /*
                #[cfg(debug_assertions)]
                {
                    if lowest.is_zero() && path.len() > 1 {
                        eprintln!("[DEBUG] Zero lowest value for path with {} pools, addr: {}", path.len(), addr);
                        eprintln!("main pool: {}", main_pool);
                    }
                }
                */

                if lowest.is_zero() { continue; } //可能存在价值太低的pool

                //self._display_pool_indexs(&path, Some(format_ether(lowest)));
                
                //dbg!(&path, &values, &lowest);
                //长度每+1衰减到0.6
                let weight = self.tokens.get(&s_in).usd(lowest) * 0.6f32.powi(path.len() as i32 - 1);
                //println!("values: {:?}, lowest: {}", values.iter().map(|v| format_ether(*v)).collect::<Vec<String>>(), format_ether(lowest));
                //let weight = self.tokens.get(&s_in).usd(lowest);

                if weight > max_value_mev.weight || (weight == max_value_mev.weight && path.len() < max_value_mev.pools.len()) {
                    max_value_mev.s_in = s_in;
                    max_value_mev.s_out = s_out;
                    max_value_mev.weight = weight;
                    max_value_mev.calc = calc;
                    max_value_mev.pools = path.iter().map(|p| p.to_no_fee_mev_pool()).collect();

                    max_values = values;
                    max_lowest = lowest;
                }
            }

            if max_lowest.is_zero() { break 'mev; }
            //dbg!(&max_values);
            //consume, 消费掉已经使用的pools，更新到cache
            cache.consume(self, &max_value_mev, &max_values, &max_lowest);
            //价值最大的mev加入到列表
            paths.push(max_value_mev);
        }

        if paths.len() > 1 {
            // 从大到小排序paths
            paths.sort_unstable_by(|a, b| {
                // 如果weight相等，选择路径短的
                b.weight.partial_cmp(&a.weight)
                    .unwrap_or(std::cmp::Ordering::Equal)
                    .then_with(|| a.pools.len().cmp(&b.pools.len()))
            });
        }

        //consume.resume(self);
        paths
    }

    /// 更新所有pool的reserves
    pub async fn sync_all_reserves(&mut self, connector: Arc<Connector>) -> Result<(), DEXError> {
        println!("sync_all_reserves");
        // 并发限制常量
        const MAX_CONCURRENT_TASKS: usize = 20;
        
        let mut v2_addrs = Vec::new();
        let mut v3_addrs = Vec::new();
        
        // 只收集地址，不克隆整个池子对象
        for item in self.pools.data.iter() {
            let pool = item.value();
            match pool {
                POOL::UniV2Pool(_) => v2_addrs.push(pool.addr()),
                POOL::UniV3Pool(_) => v3_addrs.push(pool.addr()),
            }
        }
        
        println!("同步池子reserves: V2池子数量: {}, V3池子数量: {}", v2_addrs.len(), v3_addrs.len());
        
        // 处理V2池子地址
        if !v2_addrs.is_empty() {
            let total_v2_pools = v2_addrs.len();
            let processed_counter = Arc::new(AtomicUsize::new(0));
            
            // 将v2_addrs分割成多个批次
            let v2_chunks: Vec<Vec<Address>> = v2_addrs
                .chunks(BATCH_CHECK_SIZE)
                .map(|chunk| chunk.to_vec())
                .collect();
            
            println!("开始同步V2池子reserves, 共{}个批次", v2_chunks.len());
            
            // 使用buffer_unordered并发处理多个批次
            let futures = iter(v2_chunks.into_iter().map(|addrs| {
                let conn = connector.clone();
                let processed_counter_clone = processed_counter.clone();
                
                async move {
                    let chunk_size = addrs.len();
                    match super::dex::uni_v2::contract::get_pool_reserves_batch_request(addrs.clone(), conn).await {
                        Ok(reserves) => {
                            let current = processed_counter_clone.fetch_add(chunk_size, Ordering::SeqCst);
                            eprint!("\r同步V2池子reserves进度: {}/{} ({:.2}%)", 
                                current + chunk_size, 
                                total_v2_pools,
                                ((current + chunk_size) as f64 / total_v2_pools as f64) * 100.0
                            );
                            
                            Ok((addrs, reserves))
                        },
                        Err(e) => {
                            eprintln!("\nV2池子批次同步错误: {:?}", e);
                            Err(e)
                        }
                    }
                }
            }))
            .buffer_unordered(MAX_CONCURRENT_TASKS);
            
            futures::pin_mut!(futures);
            
            // 处理结果
            while let Some(result) = futures.next().await {
                match result {
                    Ok((addrs, reserves)) => {
                        // 更新池子数据
                        for (i, addr) in addrs.iter().enumerate() {
                            if let Some(mut pool) = self.pools.data.get_mut(addr) {
                                let (reserve0, reserve1, timestamp) = reserves[i];
                                if let POOL::UniV2Pool(ref mut v2pool) = *pool.value_mut() {
                                    v2pool.data.tokens[0].reserve = reserve0;
                                    v2pool.data.tokens[1].reserve = reserve1;
                                    v2pool.data.update_time = timestamp as u64;
                                }
                            }
                        }
                    },
                    Err(e) => {
                        eprintln!("\nV2池子同步错误: {:?}", e);
                    }
                }
            }
            
            println!("\nV2池子reserves同步完成");
        }
        
        // 处理V3池子地址 (当V3 sync_reserves实现后再完善)
        if !v3_addrs.is_empty() {
            println!("注意: V3池子同步尚未完全实现，共有 {} 个V3池子", v3_addrs.len());
            // 这里未来实现V3池子的reserves同步
        }
        self.save();
        Ok(())
    }

    //None: 全量更新mev
    //Some: 更新指定pools的mev，支持两阶段更新机制
    pub async fn generate_mevs(&mut self, addrs: Option<Vec<Address>>) {
        // 获取需要处理的池子地址
        let (pools, is_specific_update) = if let Some(pools) = addrs {
            (pools, true)
        } else {
            (self.pools.data.iter().map(|entry| *entry.key()).collect(), false)
        };

        // 执行第一阶段MEV生成
        println!("开始第一阶段MEV生成，处理 {} 个池地址", pools.len());
        let phase1_generated_addrs = self.execute_mev_generation_phase(&pools).await;
        println!("阶段 1 完成，生成MEV的池子数: {}/{}", pools.len(), phase1_generated_addrs.len());

        // 如果是指定地址更新且第一阶段有结果，执行第二阶段更新
        if is_specific_update && !phase1_generated_addrs.is_empty() {
            println!("开始第二阶段MEV生成...");

            // 提取第一阶段生成的所有MEV中的池地址
            let mut second_phase_addrs = HashSet::new();
            for addr in &phase1_generated_addrs {
                if let Some(mev_list) = self.pools.mevs.get(addr) {
                    for mev in mev_list.iter() {
                        for mev_pool in &mev.pools {
                            second_phase_addrs.insert(mev_pool.addr);
                        }
                    }
                }
            }

            // 过滤掉第一阶段已处理的地址，避免重复计算
            let first_phase_set: HashSet<Address> = pools.into_iter().collect();
            let filtered_second_phase_addrs: Vec<Address> = second_phase_addrs
                .into_iter()
                .filter(|addr| !first_phase_set.contains(addr))
                .collect();

            if !filtered_second_phase_addrs.is_empty() {
                println!("第二阶段处理 {} 个池地址", filtered_second_phase_addrs.len());
                let _phase2_generated_addrs = self.execute_mev_generation_phase(&filtered_second_phase_addrs).await;
                println!("阶段 2 完成，生成MEV的池子数: {}/{}", filtered_second_phase_addrs.len(), _phase2_generated_addrs.len());
            } else {
                println!("第二阶段无需处理的池地址");
            }
        }

        println!("MEV生成完成");
    }

    /// 执行单阶段MEV生成的核心逻辑
    ///
    /// # 参数
    /// * `pools` - 需要处理的池地址列表
    ///
    /// # 返回值
    /// * `HashSet<Address>` - 生成了MEV的池地址集合
    async fn execute_mev_generation_phase(&mut self, pools: &[Address]) -> HashSet<Address> {
        use rayon::prelude::*;
        use std::sync::Mutex;

        // 创建线程安全的HashSet来收集生成MEV的池地址
        let generated_addrs = Mutex::new(HashSet::new());

        // 并行处理所有池子
        pools.par_iter().for_each(|addr| {
            // 获取MEV路径
            let mev = self.get_mev(addr);

            if mev.len() > 0 {
                // 更新池子的MEV数据
                self.pools.mevs.insert(addr.clone(), mev);
                // 直接插入到HashSet中，避免后续的contains_key查找
                generated_addrs.lock().unwrap().insert(*addr);
            } else {
                self.pools.mevs.remove(addr);
            }
        });

        let generated_pool_addrs = generated_addrs.into_inner().unwrap();
        
        generated_pool_addrs
    }


    pub async fn subscribe_block(
        &mut self,
        connector: Arc<Connector>
    ) -> Result<(Pin<Box<dyn Stream<Item = Vec<Address>> + Send>>), DEXError> 
    {
        // 创建专用通道 - 分离两种事件
        let (amm_tx, amm_rx) = mpsc::channel(16);
    
        // 克隆所需数据
        let provider = connector.provider.clone();
        let pools = self.pools.clone();
        
        tokio::spawn(async move {
            use futures::stream::FuturesUnordered;
            
            let mut block_stream = match provider.subscribe_blocks().await {
                Ok(stream) => stream.into_stream(),
                Err(e) => {
                    println!("Block subscription failed: {e}");
                    return;
                }
            };
            
            let mut last_processed_block = 0u64;
            // 创建一个FuturesUnordered来管理并发的get_logs请求
            let mut log_futures = FuturesUnordered::new();
            
            use tokio::time::{timeout, Duration};

            loop {
                tokio::select! {
                    // 处理新区块
                    Some(block) = block_stream.next() => {
                        let end_block = block.number;
                        //block.inner.base_fee_per_gas = block.base_fee_per_gas;
                        let current = last_processed_block;
                        // 跳过旧区块
                        if end_block <= current { continue; }
                        
                        // start_block等于last_processed_block，并且不小于end_block-20
                        let min_block = end_block.saturating_sub(20);
                        let start_block = last_processed_block.max(min_block);
                        last_processed_block = end_block;
                        
                        // 创建日志获取任务并添加到FuturesUnordered
                        let filter = Filter::new().from_block(start_block).to_block(end_block);
                        let provider_clone = provider.clone();
                        let pools_clone = pools.clone();
                        let amm_tx_clone = amm_tx.clone();
                        
                        // 在任务中返回是否成功处理区块的标志，用于错误处理
                        log_futures.push(async move {
                            // 定义一个错误处理函数，减少重复代码
                            let handle_error = |error_msg: String| {
                                println!("{}", error_msg.red().bold());
                                // 更新最新区块
                                crate::STATUS.update_base_fee(block.base_fee_per_gas);
                                crate::STATUS.update_latest_block(end_block);
                                (end_block, false) // 返回区块号和失败标志
                            };
                            
                            // 获取日志
                            let logs = match timeout(GET_LOGS_TIMEOUT, provider_clone.get_logs(&filter)).await {
                                Ok(Ok(logs)) => logs,
                                Ok(Err(e)) => {
                                    return handle_error(format!("[ERR] Get logs failed for blocks {start_block}-{end_block}: {e}"));
                                }
                                Err(_) => {
                                    return handle_error(format!("[ERR] Get logs timed out for blocks {start_block}-{end_block}"));
                                }
                            };
                            println!("\n{} {}", now_str().green(), format!("block: {start_block} ~ {end_block}, logs: {}", logs.len()).green());
                            // 同步池状态
                            let affected_amms = match pools_clone.sync(&logs) {
                                Ok(amms) => amms,
                                Err(e) => {
                                    return handle_error(format!("[ERR] Pool sync failed: {e}"));
                                }
                            };
                            
                            // 更新最新区块
                            crate::STATUS.update_base_fee(block.base_fee_per_gas);
                            crate::STATUS.update_latest_block(end_block);

                            // 发送AMM更新
                            if !affected_amms.is_empty() {
                                let _ = amm_tx_clone.send(affected_amms).await;
                            }
                            
                            (end_block, true) // 返回区块号和成功标志
                        });
                    }
                    
                    // 处理完成的日志获取任务
                    Some((processed_block, success)) = log_futures.next(), if !log_futures.is_empty() => {
                        // 如果处理失败，记录日志但不做额外操作
                        if !success {
                            println!("{}", format!("区块 {} 处理失败，但已更新最新区块号以避免卡住", processed_block).yellow());
                        }
                    }
                    
                    // 如果没有正在进行的任务且block_stream已关闭，则退出循环
                    else => {
                        if log_futures.is_empty() {
                            break;
                        }
                    }
                }
            }
        });
    
        // 创建专用流
        let amm_stream = Box::pin(ReceiverStream::new(amm_rx));
    
        Ok((amm_stream))
    }


    pub async fn subscribe_tx(
        &mut self,
        connector: Arc<Connector>
    ) -> Result<(Pin<Box<dyn Stream<Item = Transaction> + Send>>), DEXError> 
    {
        let (receipt_tx, receipt_rx) = mpsc::channel(64); // 收据可能需要更大缓冲区

        // 克隆所需数据
        let provider = connector.provider.clone();

        tokio::spawn(async move {
            use futures::stream::FuturesUnordered;
            use std::collections::VecDeque;
            
            let mut pending_stream = match provider.subscribe_pending_transactions().await {
                Ok(stream) => stream.into_stream(),
                Err(e) => {
                    println!("Pending transaction subscription failed: {e}");
                    return;
                }
            };
            
            // 创建一个FuturesUnordered来管理并发的交易查询
            let mut tx_futures = FuturesUnordered::new();
            
            // 创建一个固定大小的队列来存储最近处理过的交易哈希
            const MAX_RECENT: usize = 24;
            const CLEANUP_THRESHOLD: usize = MAX_RECENT + 12;
            let mut recent_tx_hashes = VecDeque::with_capacity(CLEANUP_THRESHOLD);
            
            loop {
                tokio::select! {
                    // 处理新的pending交易
                    Some(tx_hash) = pending_stream.next() => {
                        // 检查是否已经处理过这个交易哈希
                        if recent_tx_hashes.contains(&tx_hash) {
                            // 已经处理过，跳过
                            // println!("{} {} [已处理], buff({})", now_str(), tx_hash.to_string().yellow().bold(), receipt_tx.capacity());
                            continue;
                        }
                        
                        //println!("{} {}", now_str(), tx_hash.to_string().blue());
                        
                        // 添加到最近处理过的交易哈希列表
                        recent_tx_hashes.push_back(tx_hash);
                        // 如果列表超过最大容量，移除最旧的元素
                        if recent_tx_hashes.len() > CLEANUP_THRESHOLD {
                            let remove_count = recent_tx_hashes.len() - MAX_RECENT;
                            recent_tx_hashes.drain(0..remove_count);
                        }
                        
                        // 创建交易查询任务并添加到FuturesUnordered
                        let provider_clone = provider.clone();
                        let receipt_tx_clone = receipt_tx.clone();
                        
                        tx_futures.push(async move {
                            // 尝试获取交易
                            if let Ok(Some(tx)) = provider_clone.get_transaction_by_hash(tx_hash).await {
                                print!("\r{} {:?} -> {:?}", now_str(), tx.inner.signer(), tx.inner.to());
                                let _ = receipt_tx_clone.send(tx).await;
                            }
                        });
                    }
                    
                    // 处理完成的交易查询任务
                    Some(_) = tx_futures.next(), if !tx_futures.is_empty() => {
                        // 任务已经在内部处理完毕，这里不需要额外操作
                    }
                    
                    // 如果没有正在进行的任务且pending_stream已关闭，则退出循环
                    else => {
                        if tx_futures.is_empty() {
                            break;
                        }
                    }
                }
            }
        });

        let receipt_stream = Box::pin(ReceiverStream::new(receipt_rx));
        Ok(receipt_stream)
    }

}


//test
#[cfg(test)]
mod tests {
    use crate::vira::Vira;
    use super::*;
    use futures::StreamExt;

    #[tokio::test]
    async fn test_status_manager_init() {
        let mut vira = Vira::new().await;
        let _ = vira.contract.update().await;
        let mut sm = vira.sm;
        // 调用init方法
        let _ = sm.init(vira.connector.clone()).await;
        let _ = sync::check_mevs(&vira.contract, &sm.pools).await;
        let _ = sm.save();

        //sm.generate_mevs(None).await;
        //sm.save().unwrap();

        //let p = sm.pools.get(&Address::from_str("0x075D53895EEB5B927564bec8f18E823655848b6e").unwrap()).unwrap();
        //print!("{}", p.data());

        // 验证pools是否已初始化
        assert!(!sm.pools.data.is_empty(), "Pools should not be empty after initialization");

        // 验证tokens是否已初始化
        assert!(!sm.tokens.data.is_empty(), "Tokens should not be empty after initialization");
    }

    #[tokio::test]
    async fn test_status_manager_update_logic() {
        // 创建一个基本的 StatusManager 用于测试逻辑
        let sm = StatusManager::new();

        // 记录初始状态
        let initial_factory_count = sm.factories.len();

        println!("初始 factory 数量: {}", initial_factory_count);
        println!("配置中的 factory 数量: {}", CONFIG.factories.len());

        // 测试检测新增 factory 的逻辑
        let mut new_factories = Vec::new();
        for config_factory in &CONFIG.factories {
            let factory_addr = config_factory.data().addr;
            if !sm.factories.contains_key(&factory_addr) {
                println!("发现新增 factory: {} ({})",
                    factory_addr,
                    config_factory.data().name
                );
                new_factories.push(config_factory.clone());
            }
        }

        if initial_factory_count < CONFIG.factories.len() {
            // 如果缓存中的 factory 数量少于配置中的数量，说明有新增的 factory
            assert!(!new_factories.is_empty(),
                "Should find new factories when cache has fewer than config");

            let expected_new_count = CONFIG.factories.len() - initial_factory_count;
            assert_eq!(new_factories.len(), expected_new_count,
                "New factory count should match the difference");

            println!("✅ 检测到 {} 个新增 factory，update 函数应该会处理这些", new_factories.len());
        } else {
            // 如果数量相等，说明没有新增的 factory
            assert!(new_factories.is_empty(),
                "Should not find any new factories when counts match");

            println!("✅ 没有新增 factory，update 函数应该返回空列表");
        }

        // 验证已加载的 factory 确实存在于状态管理器中
        for (addr, factory) in &sm.factories {
            println!("已加载 factory: {} ({})", addr, factory.data().name);
        }

        println!("✅ StatusManager update 逻辑测试通过");
    }

    #[tokio::test]
    async fn test_subscribe() {
        // 初始化Vira和StatusManager
        let vira = Vira::new().await;
        let mut sm = vira.sm;
        
        // 确保先初始化
        sm.init(vira.connector.clone()).await;

        // 创建计数器
        let amm_counter = Arc::new(AtomicUsize::new(0));
        let receipt_counter = Arc::new(AtomicUsize::new(0));

        // 订阅事件
        let mut amm_stream = sm.subscribe_block(vira.connector.clone()).await.expect("订阅失败1");
        let mut receipt_stream = sm.subscribe_tx(vira.connector.clone()).await.expect("订阅失败2");

        // 创建一个超时计时器，运行180秒后退出
        let timeout = tokio::time::sleep(tokio::time::Duration::from_secs(180));
        tokio::pin!(timeout);
        
        // 创建两个异步任务处理不同的流
        let amm_counter_clone = amm_counter.clone();
        let amm_task = tokio::spawn(async move {
            println!("AMM任务启动，等待事件流...");
            while let Some(_) = amm_stream.next().await {
                amm_counter_clone.fetch_add(1, Ordering::SeqCst);
                //println!("[AMM #{count}], 受影响AMM数量: {}", affected_amms.len());
            }
            println!("AMM事件流已结束或被关闭！");
        });
        
        let receipt_counter_clone = receipt_counter.clone();
        let receipt_task = tokio::spawn(async move {
            println!("收据任务启动，等待事件流...");
            while let Some(_) = receipt_stream.next().await {
                receipt_counter_clone.fetch_add(1, Ordering::SeqCst);
                //println!("[收据 #{count}] 区块: {}, 交易哈希: {}", receipt.block_number.unwrap(), receipt.transaction_hash);
            }
            println!("收据事件流已结束或被关闭！");
        });
        
        // 等待超时或任务完成
        tokio::select! {
            _ = timeout => {
                println!("\n测试完成: 达到30秒超时限制");
                println!("统计信息:");
                println!("- AMM更新事件: {} 次", amm_counter.load(Ordering::SeqCst));
                println!("- 收据事件: {} 次", receipt_counter.load(Ordering::SeqCst));
            }
        }
        
        // 等待任务完成或取消
        if let Err(e) = amm_task.await {
            println!("AMM任务异常: {:?}", e);
        }
        if let Err(e) = receipt_task.await {
            println!("收据任务异常: {:?}", e);
        }
    }

    /// 测试两阶段MEV生成功能
    #[tokio::test]
    async fn test_two_phase_mev_generation() {
        use std::str::FromStr;

        // 创建测试用的StatusManager
        let mut status_manager = StatusManager::default();

        // 创建一些测试池子地址
        let test_addrs = vec![
            Address::from_str("0x1111111111111111111111111111111111111111").unwrap(),
            Address::from_str("0x2222222222222222222222222222222222222222").unwrap(),
        ];

        println!("开始测试两阶段MEV生成...");

        // 测试指定地址的两阶段更新（由于没有实际池子数据，这主要测试逻辑流程）
        status_manager.generate_mevs(Some(test_addrs.clone())).await;

        println!("两阶段MEV生成测试完成");
    }

    /// 测试简化后的MEV生成功能
    #[test]
    fn test_simplified_mev_generation() {
        use std::str::FromStr;

        // 测试地址列表
        let test_addrs = vec![
            Address::from_str("0x1111111111111111111111111111111111111111").unwrap(),
            Address::from_str("0x2222222222222222222222222222222222222222").unwrap(),
        ];

        assert_eq!(test_addrs.len(), 2);
        println!("简化MEV生成功能测试通过，测试地址数: {}", test_addrs.len());
    }

    /// 测试检查和添加新 factory 的逻辑
    #[test]
    fn test_check_new_factories_logic() {
        // 创建一个基本的 StatusManager 用于测试逻辑
        let sm = StatusManager::new();

        // 记录初始状态
        let initial_factory_count = sm.factories.len();

        println!("初始 factory 数量: {}", initial_factory_count);
        println!("配置中的 factory 数量: {}", CONFIG.factories.len());

        // 测试检测新增 factory 的逻辑
        let mut new_factories = Vec::new();
        for config_factory in &CONFIG.factories {
            let factory_addr = config_factory.data().addr;
            if !sm.factories.contains_key(&factory_addr) {
                println!("发现新增 factory: {} ({})",
                    factory_addr,
                    config_factory.data().name
                );
                new_factories.push(config_factory.clone());
            }
        }

        if initial_factory_count < CONFIG.factories.len() {
            // 如果缓存中的 factory 数量少于配置中的数量，说明有新增的 factory
            assert!(!new_factories.is_empty(),
                "Should find new factories when cache has fewer than config");

            let expected_new_count = CONFIG.factories.len() - initial_factory_count;
            assert_eq!(new_factories.len(), expected_new_count,
                "New factory count should match the difference");

            println!("✅ 检测到 {} 个新增 factory，init 函数应该会处理这些", new_factories.len());
        } else {
            // 如果数量相等，说明没有新增的 factory
            assert!(new_factories.is_empty(),
                "Should not find any new factories when counts match");

            println!("✅ 没有新增 factory，init 函数应该跳过新 factory 检查");
        }

        // 验证已加载的 factory 确实存在于状态管理器中
        for (addr, factory) in &sm.factories {
            println!("已加载 factory: {} ({})", addr, factory.data().name);
        }

        println!("✅ 新 factory 检查逻辑测试通过");
    }
}